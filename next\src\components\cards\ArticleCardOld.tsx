import { Typography } from '@bratislava/component-library'
import React, { useId } from 'react'

import CardBase, { CardBaseProps } from '@/src/components/cards/CardBase'
import CardContent from '@/src/components/cards/CardContent'
import Button from '@/src/components/common/Button/Button'
import CardImage from '@/src/components/common/Image/CardImage'
import Tag from '@/src/components/common/Tag/Tag'
import { CommonLinkProps } from '@/src/utils/getLinkProps'

export type ArticleCardProps = {
  title: string
  linkProps: CommonLinkProps
  imgSrc?: string | null | undefined
  imgSizes?: string
  date?: string | null | undefined
  tag?: string | null | undefined
  text?: string | null | undefined
} & CardBaseProps

const ArticleCard = ({
  imgSrc,
  imgSizes,
  date,
  tag,
  title,
  text,
  linkProps,
  ...rest
}: ArticleCardProps) => {
  const titleId = useId()

  return (
    <CardBase {...rest}>
      <CardImage imgSrc={imgSrc} className="aspect-16/10" />

      <CardContent className="grow justify-between">
        <div className="flex flex-col">
          {(date || tag) && (
            <div className="flex items-center justify-between pb-2">
              {/* If no date, leaving empty div to push tag to the right */}
              <Typography variant="p-tiny">{date}</Typography>

              {tag && <Tag text={tag} size="small" isColored />}
            </div>
          )}
          <Typography
            id={titleId}
            as="h3"
            variant="h5"
            className="line-clamp-3 group-hover:underline"
          >
            {title}
          </Typography>

          {text && (
            <Typography variant="p-small" className="mt-1 line-clamp-4">
              {text}
            </Typography>
          )}
        </div>
        <Button
          variant="link"
          stretched
          {...linkProps}
          aria-labelledby={titleId}
          className="mt-4 lg:mt-5"
        />
      </CardContent>
    </CardBase>
  )
}

export default ArticleCard
