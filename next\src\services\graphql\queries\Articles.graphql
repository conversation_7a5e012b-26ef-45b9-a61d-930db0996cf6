fragment ArticleCategoryEntity on ArticleCategory {
  documentId
  title
  slug
}

fragment ArticleSlugEntity on Article {
  __typename
  documentId
  slug
  title
  locale
}

fragment ArticleCardEntity on Article {
  ...ArticleSlugEntity
  perex
  addedAt
  coverMedia {
    ...UploadImageEntity
  }
  tag {
    ...TagEntity
  }
}

fragment ArticleEntity on Article {
  ...ArticleCardEntity
  alias
  content
  articleCategory {
    ...ArticleCategoryEntity
  }
  files {
    ...FileBlock
  }
  gallery {
    ...UploadImageEntity
  }
}

query ArticleBySlug($slug: String!, $locale: I18NLocaleCode!) {
  articles(filters: { slug: { eq: $slug } }, locale: $locale) {
    ...ArticleEntity
  }
}

query ArticlesStaticPaths($limit: Int = -1, $locale: I18NLocaleCode!) {
  articles(locale: $locale, sort: "addedAt:desc", pagination: { limit: $limit }) {
    ...ArticleSlugEntity
  }
}

query ArticleCategories($locale: I18NLocaleCode) {
  articleCategories(pagination: { limit: -1 }, locale: $locale) {
    ...ArticleCategoryEntity
  }
}

query Articles(
  $sort: [String]
  $limit: Int
  $start: Int
  $filters: ArticleFiltersInput
  $locale: I18NLocaleCode
) {
  articles(
    sort: $sort
    pagination: { limit: $limit, start: $start }
    filters: $filters
    locale: $locale
  ) {
    ...ArticleCardEntity
  }
}

query ArticlesRssFeed($locale: I18NLocaleCode!) {
  # From docs: "Most feeds use 20 or fewer items." https://github.com/dylang/node-rss?tab=readme-ov-file#add-items-to-a-feed
  articles(locale: $locale, sort: "addedAt:desc", pagination: { limit: 20 }) {
    documentId
    slug
    title
    addedAt
    perex
    tag {
      title
      pageCategory {
        title
      }
    }
    coverMedia {
      url
      mime
      size
    }
  }
}

query Dev_AllArticles(
  $sort: [String]
  $limit: Int
  $start: Int
  $filters: ArticleFiltersInput
  $locale: I18NLocaleCode
) {
  articles(
    sort: $sort
    pagination: { limit: $limit, start: $start }
    filters: $filters
    locale: $locale
  ) {
    ...ArticleEntity
  }
}
