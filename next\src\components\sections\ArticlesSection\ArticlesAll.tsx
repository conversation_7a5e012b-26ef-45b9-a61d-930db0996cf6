import { Typography } from '@bratislava/component-library'
import { keepPreviousData, useQuery } from '@tanstack/react-query'
import React, { useEffect, useRef, useState } from 'react'
import { StringParam, useQueryParam, withDefault } from 'use-query-params'
import { useDebounceValue } from 'usehooks-ts'

import ArticleCard from '@/src/components/cards/ArticleCard'
import { transformArticleProps } from '@/src/components/cards/transformArticleProps'
import Pagination from '@/src/components/common/Pagination/Pagination'
import SectionHeader from '@/src/components/layouts/SectionHeader'
import ArticlesFilterGroup from '@/src/components/sections/ArticlesSection/ArticlesFilterGroup'
import SearchBar from '@/src/components/sections/SearchSection/SearchBar'
import { ArticlesSectionFragment } from '@/src/services/graphql'
import {
  articlesDefaultFilters,
  articlesFetcher,
  ArticlesFilters,
  getArticlesQueryKey,
} from '@/src/services/meili/fetchers/articlesFetcher'
import { useLocale } from '@/src/utils/useLocale'
import { useRoutePreservedState } from '@/src/utils/useRoutePreservedState'
import { useTranslation } from '@/src/utils/useTranslation'

type Props = {
  section: ArticlesSectionFragment
}

/**
 * Figma: https://www.figma.com/design/17wbd0MDQcMW9NbXl6UPs8/DS--Component-library?node-id=18995-28122&m=dev
 */

const ArticlesAll = ({ section }: Props) => {
  const { t } = useTranslation()
  const locale = useLocale()

  const { title, text } = section

  const [urlParamType, setUrlParamType] = useQueryParam('type', withDefault(StringParam, ''))
  const [urlParamSearch, setUrlParamSearch] = useQueryParam('search', withDefault(StringParam, ''))
  const [urlParamTag, setUrlParamTag] = useQueryParam('topic', withDefault(StringParam, ''))
  const [urlParamAdminGroup, setUrlParamAdminGroup] = useQueryParam(
    'author',
    withDefault(StringParam, ''),
  )

  const [filters, setFilters] = useRoutePreservedState<ArticlesFilters>({
    ...articlesDefaultFilters,
  })

  // TODO: Query params neovplynuju filter, iba naopak
  // TODO: Query params neovplynuju filter, iba naopak
  // TODO: Query params neovplynuju filter, iba naopak
  // TODO: Query params neovplynuju filter, iba naopak
  // TODO: Query params neovplynuju filter, iba naopak
  // TODO: Query params neovplynuju filter, iba naopak

  const [input, setInput] = useState('')
  const [debouncedInput] = useDebounceValue(input, 300)

  useEffect(() => {
    setFilters({
      ...filters,
      search: urlParamSearch,
      articleCategoryDocumentIds: urlParamType ? [urlParamType] : [],
      tagDocumentIds: urlParamTag ? [urlParamTag] : [],
      adminGroupDocumentIds: urlParamAdminGroup ? [urlParamAdminGroup] : [],
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const { data, isPending } = useQuery({
    queryKey: getArticlesQueryKey(filters, locale),
    queryFn: () => articlesFetcher(filters, locale),
    placeholderData: keepPreviousData,
  })

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page })
  }

  const handlefiltersChange = (newFilters: ArticlesFilters) => {
    setFilters({ ...newFilters, page: 1 })
    setUrlParamSearch(newFilters.search)
    setUrlParamType(newFilters.articleCategoryDocumentIds?.[0])
    setUrlParamTag(newFilters.tagDocumentIds?.[0])
    setUrlParamAdminGroup(newFilters.adminGroupDocumentIds?.[0])
  }

  useEffect(() => {
    handlefiltersChange({ ...filters, search: debouncedInput })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedInput])

  const searchRef = useRef<null | HTMLInputElement>(null)

  useEffect(() => {
    searchRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [filters.page, filters.pageSize])

  return (
    <div className="flex flex-col gap-8">
      <div className="flex flex-col gap-6">
        <SectionHeader title={title} text={text} />
        <SearchBar
          ref={searchRef}
          placeholder={t('SearchPage.enterKeyword')}
          input={input}
          setInput={setInput}
          setSearchQuery={() => {}}
          isLoading={isPending}
        />
        <ArticlesFilterGroup filters={filters} onFiltersChange={handlefiltersChange} />
      </div>

      {data?.hits?.length ? (
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {data.hits.map((card) => (
            <ArticleCard key={card.slug} {...transformArticleProps(card)} />
          ))}
        </div>
      ) : (
        <Typography>{t('ArticlesAll.noResults')}</Typography>
      )}

      {data?.estimatedTotalHits ? (
        <Pagination
          key={filters.search}
          totalCount={Math.ceil(data.estimatedTotalHits / filters.pageSize)}
          currentPage={filters.page}
          onPageChange={handlePageChange}
        />
      ) : null}
    </div>
  )
}

export default ArticlesAll
